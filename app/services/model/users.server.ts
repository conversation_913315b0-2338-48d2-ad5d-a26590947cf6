import { mongo<PERSON><PERSON><PERSON> } from 'mongo-connection';
import { Schema } from 'mongoose';

const UsersSchema = new Schema<Users>(
    {
        _id: {
            $type: String,
            required: true,
        },
        username: {
            $type: String,
            required: true,
        },
        email: {
            $type: String,
            required: true,
            unique: true,
        },
        isoCode: {
            $type: String,
        },
        createdAt: { $type: Date, require: true },
        status: {
            $type: String,
            required: true,
        },
        cities: {
            $type: [String],
        },
        language: {
            $type: String,
            require: true,
        },
        avatarUrl: {
            $type: String,
        },
        services: {
            password: {
                bcrypt: {
                    $type: String,
                },
            },
            google: {
                $type: String,
            },
        },
        verification: {
            code: {
                $type: String,
            },
            token: {
                $type: String,
            },
            expired: {
                $type: Date,
            },
        },
        resetPassword: {
            token: {
                $type: String,
            },
            expired: {
                $type: Date,
            },
        },
        name: {
            $type: String,
        },
        identityCard: {
            images: {
                $type: [String],
            },
            status: {
                $type: String,
            },
            uploadTimestamp: {
                $type: Date,
            },
            detectedPhone: {
                $type: String,
            },
            detectedCCCD: {
                $type: String,
            },
            reason: {
                $type: String,
            },
        },
        actionHistories: {
            $type: [
                {
                    userId: {
                        $type: String,
                        required: true,
                    },
                    oldStatus: {
                        $type: String,
                    },
                    newStatus: {
                        $type: String,
                        required: true,
                    },
                    updatedByUsername: {
                        $type: String,
                        required: true,
                    },
                    reason: {
                        $type: String,
                    },
                    createdAt: {
                        $type: Date,
                        required: true,
                    },
                },
            ],
        },

    },
    { typeKey: '$type', collection: 'users' },
);

const UsersModel = mongoClientBE.model('Users', UsersSchema);
export default UsersModel;
